#!/usr/bin/env python3
import optuna
import os

print("=== 简单 Optuna 测试 ===")
print(f"Optuna 版本: {optuna.__version__}")

# 确保 logs 目录存在
os.makedirs("./logs", exist_ok=True)

# 测试内存存储
print("\n1. 测试内存存储...")
def objective(trial):
    x = trial.suggest_float("x", -10, 10)
    return x**2

study = optuna.create_study()
study.optimize(objective, n_trials=2)
print(f"✓ 内存存储测试成功! 最佳值: {study.best_value:.4f}")

# 测试SQLite存储
print("\n2. 测试SQLite存储...")
db_path = "./logs/simple_test.db"
if os.path.exists(db_path):
    os.remove(db_path)

storage_url = f"sqlite:///{db_path}"
print(f"存储URL: {storage_url}")

try:
    storage = optuna.storages.RDBStorage(url=storage_url)
    study2 = optuna.create_study(storage=storage, study_name="simple_test")
    study2.optimize(objective, n_trials=2)
    print(f"✓ SQLite存储测试成功! 最佳值: {study2.best_value:.4f}")
    print(f"✓ 数据库文件已创建: {db_path}")
except Exception as e:
    print(f"✗ SQLite存储测试失败: {e}")

print("\n=== 测试完成 ===")
