import optuna
import os

print("Testing Optuna SQLite...")

# 确保目录存在
os.makedirs("logs", exist_ok=True)

# 数据库路径
db_path = "logs/simple_test_final.db"

# 删除旧文件
if os.path.exists(db_path):
    os.remove(db_path)

# 创建存储
storage_url = f"sqlite:///{db_path}"
storage = optuna.storages.RDBStorage(
    url=storage_url,
    engine_kwargs={"connect_args": {"timeout": 60, "check_same_thread": False}}
)

# 创建study
study = optuna.create_study(storage=storage, study_name="simple_test")

# 目标函数
def objective(trial):
    x = trial.suggest_float("x", -10, 10)
    return x**2

# 运行优化
study.optimize(objective, n_trials=2)

print(f"Best value: {study.best_value}")
print(f"Best params: {study.best_params}")
print(f"Database created: {os.path.exists(db_path)}")

print("Test completed successfully!")
